//! Flutter-Rust Bridge for TuShen Core
//! 
//! This module provides the FFI interface between Flutter and Rust,
//! exposing image processing functionality to the Flutter app.

use flutter_rust_bridge::frb;
use crate::{
    utils::{ImageData, ImageFormat, ColorSpace},
    filters::{FilterEngine, FilterType},
    core::{CompressionOptions, ImageCompressor},
    core::image_compressor::{CompressionQuality, TargetSize},
    core::{ImageAuthenticator, AuthenticationConfig, AuthenticationResult},
};
use std::sync::Arc;

/// Initialize the TuShen core library
#[frb(sync)]
pub fn init_tushen_core() -> Result<String, String> {
    crate::init().map_err(|e| e.to_string())?;
    Ok("TuShen Core initialized successfully".to_string())
}

/// Get library information
#[frb(sync)]
pub fn get_library_info() -> String {
    crate::get_library_info().to_string()
}

/// Image processing bridge
pub struct ImageProcessorBridge {
    // For now, we'll implement basic functionality without the full processor
}

impl ImageProcessorBridge {
    /// Create a new image processor
    #[frb(sync)]
    pub fn new() -> Self {
        Self {}
    }

    /// Load image from bytes
    pub async fn load_image_from_bytes(&self, bytes: Vec<u8>) -> Result<ImageDataBridge, String> {
        // Basic image loading using the image crate
        let img = image::load_from_memory(&bytes)
            .map_err(|e| format!("Failed to load image: {}", e))?;

        let image_data = ImageData {
            data: bytes,
            width: img.width(),
            height: img.height(),
            format: ImageFormat::Jpeg, // Default format, should be detected
            color_space: ColorSpace::Srgb,
            metadata: None,
        };

        Ok(ImageDataBridge::from(image_data))
    }

    /// Load image from file path
    pub async fn load_image_from_path(&self, path: String) -> Result<ImageDataBridge, String> {
        let bytes = std::fs::read(&path)
            .map_err(|e| format!("Failed to read file: {}", e))?;

        self.load_image_from_bytes(bytes).await
    }

    /// Resize image
    pub async fn resize_image(
        &self,
        image: ImageDataBridge,
        width: u32,
        height: u32,
        maintain_aspect_ratio: bool,
    ) -> Result<ImageDataBridge, String> {
        let img = image::load_from_memory(&image.data)
            .map_err(|e| format!("Failed to load image: {}", e))?;

        let resized = if maintain_aspect_ratio {
            img.resize(width, height, image::imageops::FilterType::Lanczos3)
        } else {
            img.resize_exact(width, height, image::imageops::FilterType::Lanczos3)
        };

        let mut buffer = Vec::new();
        resized.write_to(&mut std::io::Cursor::new(&mut buffer), image::ImageFormat::Jpeg)
            .map_err(|e| format!("Failed to encode image: {}", e))?;

        let result = ImageData {
            data: buffer,
            width: resized.width(),
            height: resized.height(),
            format: ImageFormat::Jpeg, // Default format
            color_space: ColorSpace::Srgb, // Default color space
            metadata: None,
        };

        Ok(ImageDataBridge::from(result))
    }

    /// Crop image
    pub async fn crop_image(
        &self,
        image: ImageDataBridge,
        x: u32,
        y: u32,
        width: u32,
        height: u32,
    ) -> Result<ImageDataBridge, String> {
        let img = image::load_from_memory(&image.data)
            .map_err(|e| format!("Failed to load image: {}", e))?;

        let cropped = img.crop_imm(x, y, width, height);

        let mut buffer = Vec::new();
        cropped.write_to(&mut std::io::Cursor::new(&mut buffer), image::ImageFormat::Jpeg)
            .map_err(|e| format!("Failed to encode image: {}", e))?;

        let result = ImageData {
            data: buffer,
            width: cropped.width(),
            height: cropped.height(),
            format: ImageFormat::Jpeg, // Default format
            color_space: ColorSpace::Srgb, // Default color space
            metadata: None,
        };

        Ok(ImageDataBridge::from(result))
    }

    /// Rotate image
    pub async fn rotate_image(
        &self,
        image: ImageDataBridge,
        angle: f32,
    ) -> Result<ImageDataBridge, String> {
        let img = image::load_from_memory(&image.data)
            .map_err(|e| format!("Failed to load image: {}", e))?;

        let rotated = match angle {
            90.0 => img.rotate90(),
            180.0 => img.rotate180(),
            270.0 => img.rotate270(),
            _ => return Err("Only 90, 180, 270 degree rotations are supported".to_string()),
        };

        let mut buffer = Vec::new();
        rotated.write_to(&mut std::io::Cursor::new(&mut buffer), image::ImageFormat::Jpeg)
            .map_err(|e| format!("Failed to encode image: {}", e))?;

        let result = ImageData {
            data: buffer,
            width: rotated.width(),
            height: rotated.height(),
            format: ImageFormat::Jpeg, // Default format
            color_space: ColorSpace::Srgb, // Default color space
            metadata: None,
        };

        Ok(ImageDataBridge::from(result))
    }

    /// Save image to bytes
    pub async fn save_image_to_bytes(
        &self,
        image: ImageDataBridge,
        _format: String,
        _quality: Option<u8>,
    ) -> Result<Vec<u8>, String> {
        // For now, just return the existing data
        // In a full implementation, this would convert formats
        Ok(image.data)
    }
}

/// Filter engine bridge
pub struct FilterEngineBridge {
    engine: Arc<FilterEngine>,
}

impl FilterEngineBridge {
    /// Create a new filter engine
    #[frb(sync)]
    pub fn new() -> Self {
        Self {
            engine: Arc::new(FilterEngine::new()),
        }
    }

    /// Apply filter to image
    pub async fn apply_filter(
        &self,
        image: ImageDataBridge,
        filter_type: String,
        intensity: f32,
    ) -> Result<ImageDataBridge, String> {
        let filter = parse_filter_type(&filter_type)?;
        let result = self.engine
            .apply_filter(image.into(), filter, intensity)
            .await
            .map_err(|e| e.to_string())?;
        
        Ok(ImageDataBridge::from(result))
    }

    /// Get available filters
    #[frb(sync)]
    pub fn get_available_filters(&self) -> Vec<String> {
        self.engine.get_available_filters()
            .into_iter()
            .map(|f| f.to_string())
            .collect()
    }

    /// Get filter preview
    pub async fn get_filter_preview(
        &self,
        image: ImageDataBridge,
        filter_type: String,
        preview_size: u32,
    ) -> Result<ImageDataBridge, String> {
        let filter = parse_filter_type(&filter_type)?;
        let result = self.engine
            .get_preview(image.into(), filter, preview_size)
            .await
            .map_err(|e| e.to_string())?;
        
        Ok(ImageDataBridge::from(result))
    }
}

/// Collage engine bridge
pub struct CollageEngineBridge {
    // Simplified for now
}

impl CollageEngineBridge {
    /// Create a new collage engine
    #[frb(sync)]
    pub fn new() -> Self {
        Self {}
    }

    /// Create collage from images
    pub async fn create_collage(
        &self,
        images: Vec<ImageDataBridge>,
        _layout: String,
        _canvas_width: u32,
        _canvas_height: u32,
        _spacing: u32,
        _background_color: Option<String>,
    ) -> Result<ImageDataBridge, String> {
        // Simplified collage creation - just return the first image for now
        if images.is_empty() {
            return Err("No images provided".to_string());
        }

        Ok(images[0].clone())
    }

    /// Get available layouts
    #[frb(sync)]
    pub fn get_available_layouts(&self) -> Vec<String> {
        vec![
            "grid".to_string(),
            "mosaic".to_string(),
            "freeform".to_string(),
        ]
    }

    /// Get layout preview
    pub async fn get_layout_preview(
        &self,
        _layout: String,
        canvas_width: u32,
        canvas_height: u32,
    ) -> Result<Vec<RectBridge>, String> {
        // Simple grid layout preview
        Ok(vec![
            RectBridge { x: 0, y: 0, width: canvas_width / 2, height: canvas_height / 2 },
            RectBridge { x: canvas_width / 2, y: 0, width: canvas_width / 2, height: canvas_height / 2 },
            RectBridge { x: 0, y: canvas_height / 2, width: canvas_width / 2, height: canvas_height / 2 },
            RectBridge { x: canvas_width / 2, y: canvas_height / 2, width: canvas_width / 2, height: canvas_height / 2 },
        ])
    }
}

/// Image data bridge structure
#[derive(Clone)]
pub struct ImageDataBridge {
    pub width: u32,
    pub height: u32,
    pub format: String,
    pub color_space: String,
    pub data: Vec<u8>,
}

impl From<ImageData> for ImageDataBridge {
    fn from(data: ImageData) -> Self {
        Self {
            width: data.width,
            height: data.height,
            format: format!("{:?}", data.format),
            color_space: format!("{:?}", data.color_space),
            data: data.data,
        }
    }
}

impl From<ImageDataBridge> for ImageData {
    fn from(bridge: ImageDataBridge) -> Self {
        Self {
            width: bridge.width,
            height: bridge.height,
            format: ImageFormat::Jpeg, // Default for now
            color_space: ColorSpace::Srgb, // Default for now
            data: bridge.data,
            metadata: None,
        }
    }
}

/// Rectangle bridge structure
pub struct RectBridge {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
}

impl From<(u32, u32, u32, u32)> for RectBridge {
    fn from((x, y, width, height): (u32, u32, u32, u32)) -> Self {
        Self { x, y, width, height }
    }
}

// Helper functions
fn parse_filter_type(filter_str: &str) -> Result<FilterType, String> {
    match filter_str.to_lowercase().as_str() {
        "vintage" => Ok(FilterType::Vintage),
        "black_white" => Ok(FilterType::BlackWhite),
        "sepia" => Ok(FilterType::Sepia),
        "blur" => Ok(FilterType::Blur),
        "sharpen" => Ok(FilterType::Sharpen),
        "brightness" => Ok(FilterType::Brightness),
        "contrast" => Ok(FilterType::Contrast),
        "saturation" => Ok(FilterType::Saturation),
        _ => Err(format!("Unknown filter type: {}", filter_str)),
    }
}

/// Image compression bridge for FFI
pub struct ImageCompressorBridge {
    // Internal state
}

impl ImageCompressorBridge {
    /// Create a new image compressor
    #[frb(sync)]
    pub fn new() -> Self {
        Self {}
    }

    /// Compress an image with the given options
    pub async fn compress_image(
        &self,
        image: ImageDataBridge,
        format: String,
        quality: u8,
        target_size_kb: Option<u32>,
        resize_width: Option<u32>,
        resize_height: Option<u32>,
        maintain_aspect_ratio: bool,
    ) -> Result<CompressionResultBridge, String> {
        // Convert bridge types to core types
        let image_data = crate::utils::ImageData {
            data: image.data,
            width: image.width,
            height: image.height,
            format: string_to_image_format(&image.format)?,
            color_space: string_to_color_space(&image.color_space)?,
            metadata: None,
        };

        // Parse format
        let image_format = match format.to_lowercase().as_str() {
            "jpeg" | "jpg" => image::ImageFormat::Jpeg,
            "png" => image::ImageFormat::Png,
            "webp" => image::ImageFormat::WebP,
            _ => return Err(format!("Unsupported format: {}", format)),
        };

        // Create compression options
        let compression_options = CompressionOptions {
            format: image_format,
            quality: CompressionQuality::Custom(quality.clamp(1, 100)),
            target_size: if let Some(kb) = target_size_kb {
                TargetSize::Kilobytes(kb as usize)
            } else {
                TargetSize::None
            },
            max_iterations: 10,
            preserve_metadata: false,
            progressive: false,
            resize_dimensions: if let (Some(w), Some(h)) = (resize_width, resize_height) {
                Some((w, h))
            } else {
                None
            },
            maintain_aspect_ratio,
        };

        // Perform compression
        let compressor = ImageCompressor::new();
        match compressor.compress(image_data, compression_options).await {
            Ok(result) => Ok(CompressionResultBridge {
                image_data: ImageDataBridge {
                    data: result.image_data.data,
                    width: result.image_data.width,
                    height: result.image_data.height,
                    format: image_format_to_string(&result.image_data.format),
                    color_space: color_space_to_string(&result.image_data.color_space),
                },
                original_size: result.original_size as u64,
                compressed_size: result.compressed_size as u64,
                compression_ratio: result.compression_ratio,
                space_saved: result.space_saved as u64,
                space_saved_percentage: result.space_saved_percentage,
                final_quality: result.final_quality,
                iterations: result.iterations,
                target_achieved: result.target_achieved,
            }),
            Err(e) => Err(format!("Compression failed: {}", e)),
        }
    }

    /// Estimate compression result without actually compressing
    pub async fn estimate_compression(
        &self,
        image: ImageDataBridge,
        format: String,
        quality: u8,
    ) -> Result<CompressionEstimateBridge, String> {
        let image_data = crate::utils::ImageData {
            data: image.data,
            width: image.width,
            height: image.height,
            format: string_to_image_format(&image.format)?,
            color_space: string_to_color_space(&image.color_space)?,
            metadata: None,
        };

        let image_format = match format.to_lowercase().as_str() {
            "jpeg" | "jpg" => image::ImageFormat::Jpeg,
            "png" => image::ImageFormat::Png,
            "webp" => image::ImageFormat::WebP,
            _ => return Err(format!("Unsupported format: {}", format)),
        };

        let compression_options = CompressionOptions {
            format: image_format,
            quality: CompressionQuality::Custom(quality.clamp(1, 100)),
            target_size: TargetSize::None,
            ..Default::default()
        };

        let compressor = ImageCompressor::new();
        match compressor.estimate_compression(&image_data, &compression_options).await {
            Ok((estimated_size, compression_ratio)) => Ok(CompressionEstimateBridge {
                estimated_size: estimated_size as u64,
                compression_ratio,
                space_saved: (image_data.data.len().saturating_sub(estimated_size)) as u64,
            }),
            Err(e) => Err(format!("Estimation failed: {}", e)),
        }
    }

    /// Get supported compression formats
    #[frb(sync)]
    pub fn get_supported_formats(&self) -> Vec<String> {
        vec![
            "jpeg".to_string(),
            "png".to_string(),
            "webp".to_string(),
        ]
    }
}

/// Compression result for FFI
#[derive(Clone)]
pub struct CompressionResultBridge {
    pub image_data: ImageDataBridge,
    pub original_size: u64,
    pub compressed_size: u64,
    pub compression_ratio: f32,
    pub space_saved: u64,
    pub space_saved_percentage: f32,
    pub final_quality: u8,
    pub iterations: u32,
    pub target_achieved: bool,
}

/// Compression estimate for FFI
#[derive(Clone)]
pub struct CompressionEstimateBridge {
    pub estimated_size: u64,
    pub compression_ratio: f32,
    pub space_saved: u64,
}

/// Image authentication bridge for FFI
pub struct ImageAuthenticatorBridge {
    authenticator: ImageAuthenticator,
}

impl ImageAuthenticatorBridge {
    /// Create a new image authenticator
    #[frb(sync)]
    pub fn new() -> Self {
        Self {
            authenticator: ImageAuthenticator::new(),
        }
    }

    /// Create authenticator with custom configuration
    #[frb(sync)]
    pub fn with_config(config: AuthenticationConfigBridge) -> Self {
        let auth_config = AuthenticationConfig {
            ela_quality: config.ela_quality,
            copy_move_threshold: config.copy_move_threshold,
            block_size: config.block_size,
            noise_sensitivity: config.noise_sensitivity,
            analyze_metadata: config.analyze_metadata,
        };

        Self {
            authenticator: ImageAuthenticator::with_config(auth_config),
        }
    }

    /// Authenticate an image for tampering detection
    pub async fn authenticate_image(&self, image_data: ImageDataBridge) -> Result<AuthenticationResultBridge, String> {
        let image_data = ImageData {
            data: image_data.data,
            width: image_data.width,
            height: image_data.height,
            format: string_to_image_format(&image_data.format).map_err(|e| e.to_string())?,
            color_space: string_to_color_space(&image_data.color_space).map_err(|e| e.to_string())?,
            metadata: None,
        };

        let result = self.authenticator.authenticate(image_data)
            .await
            .map_err(|e| e.to_string())?;

        Ok(AuthenticationResultBridge::from(result))
    }
}

/// Authentication configuration for FFI
#[derive(Clone)]
pub struct AuthenticationConfigBridge {
    pub ela_quality: u8,
    pub copy_move_threshold: f32,
    pub block_size: u32,
    pub noise_sensitivity: f32,
    pub analyze_metadata: bool,
}

impl Default for AuthenticationConfigBridge {
    fn default() -> Self {
        Self {
            ela_quality: 90,
            copy_move_threshold: 0.8,
            block_size: 16,
            noise_sensitivity: 0.5,
            analyze_metadata: true,
        }
    }
}

/// Authentication result for FFI
#[derive(Clone)]
pub struct AuthenticationResultBridge {
    pub authenticity_score: f32,
    pub analyses: Vec<AnalysisResultBridge>,
    pub manipulation_regions: Vec<ManipulationRegionBridge>,
    pub processing_time_ms: u64,
    pub analysis_images: Vec<AnalysisImageBridge>,
}

/// Individual analysis result for FFI
#[derive(Clone)]
pub struct AnalysisResultBridge {
    pub analysis_type: String,
    pub confidence: f32,
    pub anomalies: Vec<String>,
    pub metadata: Vec<MetadataPairBridge>,
}

/// Manipulation region for FFI
#[derive(Clone)]
pub struct ManipulationRegionBridge {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
    pub confidence: f32,
    pub manipulation_type: String,
    pub details: String,
}

/// Analysis image for FFI
#[derive(Clone)]
pub struct AnalysisImageBridge {
    pub name: String,
    pub image_data: ImageDataBridge,
}

/// Metadata pair for FFI
#[derive(Clone)]
pub struct MetadataPairBridge {
    pub key: String,
    pub value: String,
}

impl From<AuthenticationResult> for AuthenticationResultBridge {
    fn from(result: AuthenticationResult) -> Self {
        let analyses = result.analyses.into_iter()
            .map(|a| AnalysisResultBridge {
                analysis_type: format!("{:?}", a.analysis_type),
                confidence: a.confidence,
                anomalies: a.anomalies,
                metadata: a.metadata.into_iter()
                    .map(|(k, v)| MetadataPairBridge { key: k, value: v })
                    .collect(),
            })
            .collect();

        let manipulation_regions = result.manipulation_regions.into_iter()
            .map(|r| ManipulationRegionBridge {
                x: r.bounds.0,
                y: r.bounds.1,
                width: r.bounds.2,
                height: r.bounds.3,
                confidence: r.confidence,
                manipulation_type: format!("{:?}", r.manipulation_type),
                details: r.details,
            })
            .collect();

        let analysis_images = result.analysis_images.into_iter()
            .map(|(name, image_data)| AnalysisImageBridge {
                name,
                image_data: ImageDataBridge {
                    data: image_data.data,
                    width: image_data.width,
                    height: image_data.height,
                    format: image_format_to_string(&image_data.format),
                    color_space: color_space_to_string(&image_data.color_space),
                },
            })
            .collect();

        Self {
            authenticity_score: result.authenticity_score,
            analyses,
            manipulation_regions,
            processing_time_ms: result.processing_time_ms,
            analysis_images,
        }
    }
}

/// Helper function to convert string to ImageFormat
fn string_to_image_format(format_str: &str) -> Result<crate::utils::ImageFormat, String> {
    match format_str.to_lowercase().as_str() {
        "jpeg" | "jpg" => Ok(crate::utils::ImageFormat::Jpeg),
        "png" => Ok(crate::utils::ImageFormat::Png),
        "webp" => Ok(crate::utils::ImageFormat::WebP),
        "tiff" | "tif" => Ok(crate::utils::ImageFormat::Tiff),
        "bmp" => Ok(crate::utils::ImageFormat::Bmp),
        "gif" => Ok(crate::utils::ImageFormat::Gif),
        "ico" => Ok(crate::utils::ImageFormat::Ico),
        "avif" => Ok(crate::utils::ImageFormat::Avif),
        "heic" => Ok(crate::utils::ImageFormat::Heic),
        _ => Err(format!("Unsupported image format: {}", format_str)),
    }
}

/// Helper function to convert string to ColorSpace
fn string_to_color_space(color_space_str: &str) -> Result<crate::utils::ColorSpace, String> {
    match color_space_str.to_lowercase().as_str() {
        "srgb" => Ok(crate::utils::ColorSpace::Srgb),
        "adobe_rgb" => Ok(crate::utils::ColorSpace::AdobeRgb),
        "display_p3" => Ok(crate::utils::ColorSpace::DisplayP3),
        "rec2020" => Ok(crate::utils::ColorSpace::Rec2020),
        "prophoto_rgb" => Ok(crate::utils::ColorSpace::ProPhotoRgb),
        _ => Ok(crate::utils::ColorSpace::Unknown),
    }
}

/// Helper function to convert ImageFormat to string
fn image_format_to_string(format: &crate::utils::ImageFormat) -> String {
    match format {
        crate::utils::ImageFormat::Jpeg => "jpeg".to_string(),
        crate::utils::ImageFormat::Png => "png".to_string(),
        crate::utils::ImageFormat::WebP => "webp".to_string(),
        crate::utils::ImageFormat::Tiff => "tiff".to_string(),
        crate::utils::ImageFormat::Bmp => "bmp".to_string(),
        crate::utils::ImageFormat::Gif => "gif".to_string(),
        crate::utils::ImageFormat::Ico => "ico".to_string(),
        crate::utils::ImageFormat::Avif => "avif".to_string(),
        crate::utils::ImageFormat::Heic => "heic".to_string(),
        crate::utils::ImageFormat::Raw(_) => "raw".to_string(),
    }
}

/// Helper function to convert ColorSpace to string
fn color_space_to_string(color_space: &crate::utils::ColorSpace) -> String {
    match color_space {
        crate::utils::ColorSpace::Srgb => "srgb".to_string(),
        crate::utils::ColorSpace::AdobeRgb => "adobe_rgb".to_string(),
        crate::utils::ColorSpace::DisplayP3 => "display_p3".to_string(),
        crate::utils::ColorSpace::Rec2020 => "rec2020".to_string(),
        crate::utils::ColorSpace::ProPhotoRgb => "prophoto_rgb".to_string(),
        crate::utils::ColorSpace::Unknown => "unknown".to_string(),
    }
}
