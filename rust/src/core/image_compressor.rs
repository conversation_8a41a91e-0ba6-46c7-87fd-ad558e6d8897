//! High-performance image compression module
//! 
//! Provides advanced image compression capabilities with quality control,
//! size targeting, and format optimization.

use crate::utils::ImageData;
use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON>ult, TuShenError};
use image::{DynamicImage, ImageFormat, ImageEncoder};
use std::io::Cursor;
use serde::{Serialize, Deserialize};

/// Compression quality levels
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CompressionQuality {
    /// Maximum quality (minimal compression)
    Maximum,
    /// High quality (light compression)
    High,
    /// Medium quality (balanced compression)
    Medium,
    /// Low quality (aggressive compression)
    Low,
    /// Custom quality (0-100)
    Custom(u8),
}

impl CompressionQuality {
    /// Convert to JPEG quality value (0-100)
    pub fn to_jpeg_quality(self) -> u8 {
        match self {
            CompressionQuality::Maximum => 95,
            CompressionQuality::High => 85,
            CompressionQuality::Medium => 70,
            CompressionQuality::Low => 50,
            CompressionQuality::Custom(q) => q.clamp(1, 100),
        }
    }

    /// Convert to PNG compression level (0-9)
    pub fn to_png_compression(self) -> u8 {
        match self {
            CompressionQuality::Maximum => 1,
            CompressionQuality::High => 3,
            CompressionQuality::Medium => 6,
            CompressionQuality::Low => 9,
            CompressionQuality::Custom(q) => {
                // Map 0-100 to 9-0 (higher quality = lower compression)
                ((100 - q.clamp(1, 100)) as f32 / 100.0 * 8.0) as u8 + 1
            }
        }
    }
}

/// Target size specification
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TargetSize {
    /// No size limit
    None,
    /// Target size in bytes
    Bytes(usize),
    /// Target size in kilobytes
    Kilobytes(usize),
    /// Target size in megabytes
    Megabytes(usize),
    /// Percentage of original size (0.0-1.0)
    Percentage(f32),
}

impl TargetSize {
    /// Convert to bytes
    pub fn to_bytes(&self, original_size: usize) -> Option<usize> {
        match self {
            TargetSize::None => None,
            TargetSize::Bytes(size) => Some(*size),
            TargetSize::Kilobytes(kb) => Some(kb * 1024),
            TargetSize::Megabytes(mb) => Some(mb * 1024 * 1024),
            TargetSize::Percentage(pct) => Some((original_size as f32 * pct.clamp(0.0, 1.0)) as usize),
        }
    }
}

/// Compression options
#[derive(Debug, Clone)]
pub struct CompressionOptions {
    /// Target output format
    pub format: ImageFormat,
    /// Compression quality
    pub quality: CompressionQuality,
    /// Target file size
    pub target_size: TargetSize,
    /// Maximum iterations for size targeting
    pub max_iterations: u32,
    /// Preserve metadata
    pub preserve_metadata: bool,
    /// Progressive JPEG encoding
    pub progressive: bool,
    /// Resize dimensions (width, height) - None to keep original
    pub resize_dimensions: Option<(u32, u32)>,
    /// Maintain aspect ratio when resizing
    pub maintain_aspect_ratio: bool,
}

impl Default for CompressionOptions {
    fn default() -> Self {
        Self {
            format: ImageFormat::Jpeg,
            quality: CompressionQuality::High,
            target_size: TargetSize::None,
            max_iterations: 10,
            preserve_metadata: false,
            progressive: false,
            resize_dimensions: None,
            maintain_aspect_ratio: true,
        }
    }
}

/// Compression result with statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompressionResult {
    /// Compressed image data
    pub image_data: ImageData,
    /// Original file size in bytes
    pub original_size: usize,
    /// Compressed file size in bytes
    pub compressed_size: usize,
    /// Compression ratio (0.0-1.0)
    pub compression_ratio: f32,
    /// Space saved in bytes
    pub space_saved: usize,
    /// Space saved as percentage
    pub space_saved_percentage: f32,
    /// Final quality used
    pub final_quality: u8,
    /// Number of iterations performed
    pub iterations: u32,
    /// Whether target size was achieved
    pub target_achieved: bool,
}

/// High-performance image compressor
pub struct ImageCompressor {
    // Internal state for optimization
}

impl ImageCompressor {
    /// Create a new image compressor
    pub fn new() -> Self {
        Self {}
    }

    /// Compress an image with the given options
    pub async fn compress(
        &self,
        image_data: ImageData,
        options: CompressionOptions,
    ) -> TuShenResult<CompressionResult> {
        // Convert ImageData to DynamicImage
        let dynamic_image = self.image_data_to_dynamic_image(&image_data)?;
        let original_size = image_data.data.len();

        // Apply resizing if specified
        let processed_image = if let Some((width, height)) = options.resize_dimensions {
            self.resize_image(dynamic_image, width, height, options.maintain_aspect_ratio)?
        } else {
            dynamic_image
        };

        // Perform compression with size targeting
        let (compressed_data, final_quality, iterations, target_achieved) = 
            self.compress_with_targeting(&processed_image, &options, original_size).await?;

        // Calculate statistics
        let compressed_size = compressed_data.len();
        let compression_ratio = compressed_size as f32 / original_size as f32;
        let space_saved = original_size.saturating_sub(compressed_size);
        let space_saved_percentage = (space_saved as f32 / original_size as f32) * 100.0;

        // Create result ImageData
        let result_image_data = ImageData {
            data: compressed_data,
            width: processed_image.width(),
            height: processed_image.height(),
            format: self.format_to_string(options.format),
            color_space: image_data.color_space, // Preserve color space
        };

        Ok(CompressionResult {
            image_data: result_image_data,
            original_size,
            compressed_size,
            compression_ratio,
            space_saved,
            space_saved_percentage,
            final_quality,
            iterations,
            target_achieved,
        })
    }

    /// Estimate compression result without actually compressing
    pub async fn estimate_compression(
        &self,
        image_data: &ImageData,
        options: &CompressionOptions,
    ) -> TuShenResult<(usize, f32)> {
        // Quick estimation based on format and quality
        let original_size = image_data.data.len();
        
        let estimated_ratio = match options.format {
            ImageFormat::Jpeg => {
                let quality_factor = options.quality.to_jpeg_quality() as f32 / 100.0;
                0.1 + (quality_factor * 0.8) // JPEG typically 10-90% of original
            },
            ImageFormat::Png => {
                let compression_level = options.quality.to_png_compression() as f32 / 9.0;
                0.3 + ((1.0 - compression_level) * 0.6) // PNG typically 30-90% of original
            },
            ImageFormat::WebP => {
                let quality_factor = options.quality.to_jpeg_quality() as f32 / 100.0;
                0.05 + (quality_factor * 0.7) // WebP typically 5-75% of original
            },
            _ => 0.8, // Conservative estimate for other formats
        };

        let estimated_size = (original_size as f32 * estimated_ratio) as usize;
        Ok((estimated_size, estimated_ratio))
    }

    /// Get supported compression formats
    pub fn get_supported_formats(&self) -> Vec<ImageFormat> {
        vec![
            ImageFormat::Jpeg,
            ImageFormat::Png,
            ImageFormat::WebP,
            ImageFormat::Tiff,
            ImageFormat::Bmp,
        ]
    }

    /// Convert ImageData to DynamicImage
    fn image_data_to_dynamic_image(&self, image_data: &ImageData) -> TuShenResult<DynamicImage> {
        let cursor = Cursor::new(&image_data.data);
        image::load(cursor, self.string_to_format(&image_data.format)?)
            .map_err(|e| TuShenError::Processing(format!("Failed to decode image: {}", e)))
    }

    /// Resize image while optionally maintaining aspect ratio
    fn resize_image(
        &self,
        image: DynamicImage,
        target_width: u32,
        target_height: u32,
        maintain_aspect_ratio: bool,
    ) -> TuShenResult<DynamicImage> {
        if maintain_aspect_ratio {
            Ok(image.resize(target_width, target_height, image::imageops::FilterType::Lanczos3))
        } else {
            Ok(image.resize_exact(target_width, target_height, image::imageops::FilterType::Lanczos3))
        }
    }

    /// Compress with size targeting using iterative approach
    async fn compress_with_targeting(
        &self,
        image: &DynamicImage,
        options: &CompressionOptions,
        original_size: usize,
    ) -> TuShenResult<(Vec<u8>, u8, u32, bool)> {
        let target_bytes = options.target_size.to_bytes(original_size);
        
        if target_bytes.is_none() {
            // No size targeting, use specified quality
            let quality = options.quality.to_jpeg_quality();
            let data = self.encode_image(image, options.format, quality, options.progressive)?;
            return Ok((data, quality, 1, true));
        }

        let target_size = target_bytes.unwrap();
        let mut current_quality = options.quality.to_jpeg_quality();
        let mut best_data = Vec::new();
        let mut best_quality = current_quality;
        let mut iterations = 0;
        let mut target_achieved = false;

        // Binary search for optimal quality
        let mut min_quality = 1u8;
        let mut max_quality = 100u8;

        for _ in 0..options.max_iterations {
            iterations += 1;
            
            let data = self.encode_image(image, options.format, current_quality, options.progressive)?;
            
            if data.len() <= target_size {
                // Size target achieved
                best_data = data;
                best_quality = current_quality;
                target_achieved = true;
                
                // Try to increase quality while staying under target
                min_quality = current_quality;
                if max_quality - min_quality <= 1 {
                    break;
                }
                current_quality = (min_quality + max_quality) / 2;
            } else {
                // Size too large, reduce quality
                max_quality = current_quality;
                if max_quality - min_quality <= 1 {
                    break;
                }
                current_quality = (min_quality + max_quality) / 2;
            }
        }

        // If no suitable quality found, use the best attempt
        if best_data.is_empty() {
            best_data = self.encode_image(image, options.format, min_quality, options.progressive)?;
            best_quality = min_quality;
        }

        Ok((best_data, best_quality, iterations, target_achieved))
    }

    /// Encode image to specified format and quality
    fn encode_image(
        &self,
        image: &DynamicImage,
        format: ImageFormat,
        quality: u8,
        progressive: bool,
    ) -> TuShenResult<Vec<u8>> {
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);

        match format {
            ImageFormat::Jpeg => {
                let mut encoder = image::codecs::jpeg::JpegEncoder::new_with_quality(&mut cursor, quality);
                if progressive {
                    encoder = encoder.with_progressive(true);
                }
                encoder.encode_image(image)
                    .map_err(|e| TuShenError::Processing(format!("JPEG encoding failed: {}", e)))?;
            },
            ImageFormat::Png => {
                let compression_level = CompressionQuality::Custom(quality).to_png_compression();
                let encoder = image::codecs::png::PngEncoder::new_with_quality(
                    &mut cursor,
                    image::codecs::png::CompressionType::Default,
                    image::codecs::png::FilterType::Adaptive,
                );
                encoder.encode_image(image)
                    .map_err(|e| TuShenError::Processing(format!("PNG encoding failed: {}", e)))?;
            },
            ImageFormat::WebP => {
                // WebP encoding (simplified)
                image.write_to(&mut cursor, format)
                    .map_err(|e| TuShenError::Processing(crate::error::ProcessingError::EncodeError(format!("WebP encoding failed: {}", e))))?;
            },
            _ => {
                // Fallback to default encoding
                image.write_to(&mut cursor, format)
                    .map_err(|e| TuShenError::Processing(crate::error::ProcessingError::EncodeError(format!("Image encoding failed: {}", e))))?;
            }
        }

        Ok(buffer)
    }

    /// Convert format string to ImageFormat
    fn string_to_format(&self, format_str: &str) -> TuShenResult<ImageFormat> {
        match format_str.to_lowercase().as_str() {
            "jpeg" | "jpg" => Ok(ImageFormat::Jpeg),
            "png" => Ok(ImageFormat::Png),
            "webp" => Ok(ImageFormat::WebP),
            "tiff" | "tif" => Ok(ImageFormat::Tiff),
            "bmp" => Ok(ImageFormat::Bmp),
            _ => Err(TuShenError::Processing(crate::error::ProcessingError::UnsupportedFormat {
                format: format_str.to_string()
            })),
        }
    }

    /// Convert ImageFormat to string
    fn format_to_string(&self, format: ImageFormat) -> String {
        match format {
            ImageFormat::Jpeg => "jpeg".to_string(),
            ImageFormat::Png => "png".to_string(),
            ImageFormat::WebP => "webp".to_string(),
            ImageFormat::Tiff => "tiff".to_string(),
            ImageFormat::Bmp => "bmp".to_string(),
            _ => "jpeg".to_string(), // Default fallback
        }
    }
}

impl Default for ImageCompressor {
    fn default() -> Self {
        Self::new()
    }
}
