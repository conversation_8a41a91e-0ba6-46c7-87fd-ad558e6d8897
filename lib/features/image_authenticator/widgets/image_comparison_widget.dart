import 'dart:io';
import 'package:flutter/material.dart';
import '../models/authentication_data.dart';

class ImageComparisonWidget extends StatefulWidget {
  final File originalImage;
  final AnalysisResult? analysisResult;
  final double sliderValue;
  final ValueChanged<double> onSliderChanged;

  const ImageComparisonWidget({
    super.key,
    required this.originalImage,
    this.analysisResult,
    required this.sliderValue,
    required this.onSliderChanged,
  });

  @override
  State<ImageComparisonWidget> createState() => _ImageComparisonWidgetState();
}

class _ImageComparisonWidgetState extends State<ImageComparisonWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Header with analysis type
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.white.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.compare,
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  widget.analysisResult?.analysisType ?? 'Original Image',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (widget.analysisResult != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getConfidenceColor(widget.analysisResult!.confidence),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${(widget.analysisResult!.confidence * 100).toInt()}%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Image comparison area
          Expanded(
            child: Stack(
              children: [
                // Background image container
                Positioned.fill(
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                    child: _buildImageDisplay(),
                  ),
                ),

                // Slider overlay
                if (widget.analysisResult != null)
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: _buildComparisonSlider(),
                  ),

                // Manipulation regions overlay
                if (widget.analysisResult != null && widget.sliderValue > 0.5)
                  Positioned.fill(
                    child: _buildManipulationRegionsOverlay(),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageDisplay() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // Original image
          Positioned.fill(
            child: Image.file(
              widget.originalImage,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey.withValues(alpha: 0.2),
                  child: const Center(
                    child: Icon(
                      Icons.error,
                      color: Colors.red,
                      size: 48,
                    ),
                  ),
                );
              },
            ),
          ),

          // Analysis visualization (if available)
          if (widget.analysisResult?.visualizationPath != null && widget.sliderValue > 0.0)
            Positioned.fill(
              child: Opacity(
                opacity: widget.sliderValue,
                child: Image.file(
                  File(widget.analysisResult!.visualizationPath!),
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.blue.withValues(alpha: 0.2),
                      child: const Center(
                        child: Text(
                          'Analysis Visualization',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildComparisonSlider() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Original',
                style: TextStyle(
                  color: widget.sliderValue < 0.5 
                      ? Colors.white 
                      : Colors.white.withValues(alpha: 0.6),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'Analysis',
                style: TextStyle(
                  color: widget.sliderValue > 0.5 
                      ? Colors.white 
                      : Colors.white.withValues(alpha: 0.6),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.white,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
              thumbColor: Colors.white,
              overlayColor: Colors.white.withValues(alpha: 0.2),
              trackHeight: 4,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            ),
            child: Slider(
              value: widget.sliderValue,
              onChanged: widget.onSliderChanged,
              min: 0.0,
              max: 1.0,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManipulationRegionsOverlay() {
    if (widget.analysisResult?.regions.isEmpty ?? true) {
      return const SizedBox.shrink();
    }

    return CustomPaint(
      painter: ManipulationRegionsPainter(
        regions: widget.analysisResult!.regions,
        opacity: (widget.sliderValue - 0.5) * 2, // Fade in after 50%
      ),
    );
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) {
      return Colors.red.withValues(alpha: 0.8);
    } else if (confidence >= 0.6) {
      return Colors.orange.withValues(alpha: 0.8);
    } else {
      return Colors.green.withValues(alpha: 0.8);
    }
  }
}

class ManipulationRegionsPainter extends CustomPainter {
  final List<ManipulationRegion> regions;
  final double opacity;

  ManipulationRegionsPainter({
    required this.regions,
    required this.opacity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (opacity <= 0) return;

    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..color = Colors.red.withValues(alpha: opacity);

    final fillPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.red.withValues(alpha: opacity * 0.2);

    for (final region in regions) {
      final rect = Rect.fromLTWH(
        region.x,
        region.y,
        region.width,
        region.height,
      );

      // Draw filled rectangle
      canvas.drawRect(rect, fillPaint);
      
      // Draw border
      canvas.drawRect(rect, paint);

      // Draw confidence label
      final textPainter = TextPainter(
        text: TextSpan(
          text: '${(region.confidence * 100).toInt()}%',
          style: TextStyle(
            color: Colors.white.withValues(alpha: opacity),
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(region.x + 4, region.y + 4),
      );
    }
  }

  @override
  bool shouldRepaint(ManipulationRegionsPainter oldDelegate) {
    return oldDelegate.regions != regions || oldDelegate.opacity != opacity;
  }
}
