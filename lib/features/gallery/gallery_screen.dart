import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'controllers/gallery_controller.dart';
import 'models/gallery_models.dart';
import 'widgets/gallery_grid.dart';
import 'widgets/gallery_search_bar.dart';
import 'widgets/gallery_filter_bar.dart';
import 'widgets/gallery_selection_bar.dart';
import 'widgets/gallery_empty_state.dart';
import '../../core/design_system/colors.dart';
import '../../core/services/service_locator.dart';
import '../../core/services/gallery_service.dart';

/// Gallery Screen
///
/// Displays user's photos and created collages in an organized grid view.
/// Provides search, filtering, and organization capabilities.
class GalleryScreen extends StatefulWidget {
  const GalleryScreen({super.key});

  @override
  State<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends State<GalleryScreen> {
  late final GalleryController _controller;
  bool _isSearchVisible = false;

  @override
  void initState() {
    super.initState();
    _controller = getIt<GalleryController>();
    _controller.initialize();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return Scaffold(
          backgroundColor: Theme.of(context).colorScheme.surface,
          body: CustomScrollView(
            slivers: [
              _buildAppBar(context),
              if (_isSearchVisible) _buildSearchBar(),
              _buildFilterBar(),
              if (_controller.isSelectionMode) _buildSelectionBar(),
              _buildContent(),
            ],
          ),
          floatingActionButton: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Debug button for testing
              FloatingActionButton.small(
                onPressed: _addTestItem,
                backgroundColor: Colors.orange,
                child: const Icon(Icons.bug_report),
              ),
              const SizedBox(height: 8),
              if (_buildFloatingActionButton(context) != null)
                _buildFloatingActionButton(context)!,
            ],
          ),
        );
      },
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return SliverAppBar(
      expandedHeight: 140,
      floating: true,
      pinned: true,
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: _controller.isSelectionMode
            ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: TuShenColors.primaryGradient.first.withValues(
                    alpha: 0.1,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: TuShenColors.primaryGradient.first.withValues(
                      alpha: 0.3,
                    ),
                  ),
                ),
                child: Text(
                  '${_controller.selectedCount} selected',
                  style: TextStyle(
                    color: TuShenColors.primaryGradient.first,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              )
            : ShaderMask(
                shaderCallback: (bounds) => LinearGradient(
                  colors: TuShenColors.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ).createShader(bounds),
                child: const Text(
                  'Gallery',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 28,
                    letterSpacing: -0.5,
                  ),
                ),
              ),
        titlePadding: const EdgeInsets.only(left: 20, bottom: 20),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                colorScheme.surface,
                colorScheme.surface.withValues(alpha: 0.95),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Stack(
            children: [
              // Subtle background pattern
              Positioned.fill(
                child: CustomPaint(
                  painter: _GalleryBackgroundPainter(
                    color: TuShenColors.primaryGradient.first.withValues(
                      alpha: 0.03,
                    ),
                  ),
                ),
              ),
              // Gradient overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        TuShenColors.primaryGradient.first.withValues(
                          alpha: 0.05,
                        ),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: _buildAppBarActions(context),
    );
  }

  List<Widget> _buildAppBarActions(BuildContext context) {
    if (_controller.isSelectionMode) {
      return [
        IconButton(
          icon: const Icon(Icons.select_all, color: Colors.white),
          onPressed: _controller.selectAll,
        ),
        IconButton(
          icon: const Icon(Icons.delete, color: Colors.white),
          onPressed: _controller.selectedCount > 0
              ? () => _showDeleteConfirmation(context)
              : null,
        ),
        IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: _controller.toggleSelectionMode,
        ),
      ];
    }

    return [
      IconButton(
        icon: Icon(
          _isSearchVisible ? Icons.search_off : Icons.search,
          color: Colors.white,
        ),
        onPressed: () {
          setState(() {
            _isSearchVisible = !_isSearchVisible;
          });
          if (!_isSearchVisible) {
            _controller.clearSearch();
          }
        },
      ),
      IconButton(
        icon: Icon(
          _controller.viewMode == GalleryViewMode.grid
              ? Icons.view_list
              : Icons.grid_view,
          color: Colors.white,
        ),
        onPressed: () {
          _controller.setViewMode(
            _controller.viewMode == GalleryViewMode.grid
                ? GalleryViewMode.list
                : GalleryViewMode.grid,
          );
        },
      ),
      PopupMenuButton<String>(
        icon: const Icon(Icons.more_vert, color: Colors.white),
        onSelected: _handleMenuAction,
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'sort',
            child: ListTile(
              leading: Icon(Icons.sort),
              title: Text('Sort'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuItem(
            value: 'select',
            child: ListTile(
              leading: Icon(Icons.checklist),
              title: Text('Select'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuItem(
            value: 'refresh',
            child: ListTile(
              leading: Icon(Icons.refresh),
              title: Text('Refresh'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    ];
  }

  Widget _buildSearchBar() {
    return SliverToBoxAdapter(
      child: GallerySearchBar(
        controller: _controller,
        onSearch: _controller.search,
        onClear: _controller.clearSearch,
      ),
    );
  }

  Widget _buildFilterBar() {
    return SliverToBoxAdapter(
      child: GalleryFilterBar(
        controller: _controller,
        onFilterChanged: _controller.setFilter,
      ),
    );
  }

  Widget _buildSelectionBar() {
    return SliverToBoxAdapter(
      child: GallerySelectionBar(
        controller: _controller,
        onSelectAll: _controller.selectAll,
        onClearSelection: _controller.clearSelection,
        onDelete: () => _showDeleteConfirmation(context),
      ),
    );
  }

  Widget _buildContent() {
    if (_controller.isLoading) {
      return const SliverFillRemaining(
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_controller.error != null) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                _controller.error!,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _controller.refresh,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (!_controller.hasItems) {
      return SliverFillRemaining(
        child: GalleryEmptyState(
          onImportPhotos: _handleImportPhotos,
          onTakePicture: _handleTakePicture,
          onCreateCollage: _handleCreateCollage,
        ),
      );
    }

    return SliverToBoxAdapter(
      child: GalleryGrid(
        controller: _controller,
        items: _controller.displayItems,
        onItemTap: _handleItemTap,
        onItemLongPress: _handleItemLongPress,
      ),
    );
  }

  Widget? _buildFloatingActionButton(BuildContext context) {
    if (_controller.isSelectionMode) return null;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: TuShenColors.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: TuShenColors.primaryGradient.first.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: FloatingActionButton(
        onPressed: _showCreateOptions,
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: const Icon(Icons.add, color: Colors.white, size: 28),
      ),
    );
  }

  void _showCreateOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Create New',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            _buildCreateOption(
              context,
              icon: Icons.photo_library_outlined,
              title: 'Import Photos',
              subtitle: 'Add photos from your gallery',
              onTap: _handleImportPhotos,
            ),
            _buildCreateOption(
              context,
              icon: Icons.camera_alt_outlined,
              title: 'Take Photo',
              subtitle: 'Capture a new photo',
              onTap: _handleTakePicture,
            ),
            _buildCreateOption(
              context,
              icon: Icons.dashboard_customize_outlined,
              title: 'Create Collage',
              subtitle: 'Make a beautiful collage',
              onTap: _handleCreateCollage,
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.pop(context);
            onTap();
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: TuShenColors.primaryGradient.first.withValues(
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: TuShenColors.primaryGradient.first,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Theme.of(context).colorScheme.outline,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'sort':
        _showSortOptions(context);
        break;
      case 'select':
        _controller.toggleSelectionMode();
        break;
      case 'refresh':
        _controller.refresh();
        break;
    }
  }

  void _handleItemTap(GalleryItem item) {
    if (_controller.isSelectionMode) {
      _controller.toggleItemSelection(item.id);
    } else {
      // TODO: Open item viewer
    }
  }

  void _handleItemLongPress(GalleryItem item) {
    HapticFeedback.mediumImpact();
    if (!_controller.isSelectionMode) {
      _controller.toggleSelectionMode();
    }
    _controller.toggleItemSelection(item.id);
  }

  void _handleImportPhotos() {
    // TODO: Implement photo import functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import photos functionality coming soon')),
    );
  }

  void _handleTakePicture() {
    // TODO: Implement camera functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Camera functionality coming soon')),
    );
  }

  void _handleCreateCollage() {
    // Navigate to collage creation
    Navigator.of(context).pushNamed('/collage');
  }

  // Debug method to test gallery functionality
  void _addTestItem() async {
    final galleryService = getIt<GalleryService>();

    // Create a test file path (this would normally be a real image file)
    final testPath = '/test/path/test_image.jpg';

    // For testing, we'll create a mock gallery item directly
    final galleryController = getIt<GalleryController>();
    final testItem = GalleryItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: 'Test Image',
      path: testPath,
      type: GalleryItemType.image,
      createdAt: DateTime.now(),
      modifiedAt: DateTime.now(),
      size: 1024 * 1024, // 1MB
      tags: ['test'],
    );

    await galleryController.addItem(testItem);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Test item added to gallery')),
      );
    }
  }

  void _showSortOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Sort by', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            ...GallerySortBy.values.map(
              (sortBy) => ListTile(
                title: Text(_getSortByName(sortBy)),
                trailing: _controller.sortBy == sortBy
                    ? Icon(
                        _controller.sortOrder == GallerySortOrder.ascending
                            ? Icons.arrow_upward
                            : Icons.arrow_downward,
                      )
                    : null,
                onTap: () {
                  _controller.setSortBy(sortBy);
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Items'),
        content: Text(
          'Are you sure you want to delete ${_controller.selectedCount} item(s)? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _controller.deleteSelectedItems();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _getSortByName(GallerySortBy sortBy) {
    switch (sortBy) {
      case GallerySortBy.dateCreated:
        return 'Date Created';
      case GallerySortBy.dateModified:
        return 'Date Modified';
      case GallerySortBy.name:
        return 'Name';
      case GallerySortBy.size:
        return 'Size';
      case GallerySortBy.type:
        return 'Type';
    }
  }
}

/// Custom painter for Gallery background pattern
class _GalleryBackgroundPainter extends CustomPainter {
  final Color color;

  _GalleryBackgroundPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Create a subtle geometric pattern
    const spacing = 40.0;
    const dotSize = 2.0;

    for (double x = 0; x < size.width; x += spacing) {
      for (double y = 0; y < size.height; y += spacing) {
        canvas.drawCircle(Offset(x, y), dotSize, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
