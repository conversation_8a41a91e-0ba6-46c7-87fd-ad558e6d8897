import 'dart:ffi';
import 'dart:io';
import 'dart:typed_data';
import 'package:ffi/ffi.dart';

/// FFI service for communicating with Rust backend
class RustBridgeService {
  static RustBridgeService? _instance;
  static RustBridgeService get instance => _instance ??= RustBridgeService._();

  RustBridgeService._();

  late final DynamicLibrary _lib;
  bool _isInitialized = false;

  /// Initialize the Rust library
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _lib = _loadLibrary();
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize Rust bridge: $e');
    }
  }

  /// Load the appropriate dynamic library for the current platform
  DynamicLibrary _loadLibrary() {
    if (Platform.isAndroid) {
      return DynamicLibrary.open('libtushen_core.so');
    } else if (Platform.isIOS) {
      return DynamicLibrary.process();
    } else if (Platform.isMacOS) {
      // Try different paths for macOS
      final possiblePaths = [
        'libtushen_core.dylib',
        './libtushen_core.dylib',
        '../MacOS/libtushen_core.dylib',
        '../../MacOS/libtushen_core.dylib',
      ];

      for (final path in possiblePaths) {
        try {
          return DynamicLibrary.open(path);
        } catch (e) {
          // Continue to next path
          continue;
        }
      }

      // If all paths fail, try the default
      return DynamicLibrary.open('libtushen_core.dylib');
    } else if (Platform.isWindows) {
      return DynamicLibrary.open('tushen_core.dll');
    } else if (Platform.isLinux) {
      return DynamicLibrary.open('libtushen_core.so');
    } else {
      throw UnsupportedError(
        'Unsupported platform: ${Platform.operatingSystem}',
      );
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Get the dynamic library instance
  DynamicLibrary get library {
    if (!_isInitialized) {
      throw StateError(
        'RustBridgeService not initialized. Call initialize() first.',
      );
    }
    return _lib;
  }

  /// Authenticate an image for tampering detection
  Future<Map<String, dynamic>> authenticateImage(
    ImageDataBridge imageData,
    Map<String, dynamic> config,
  ) async {
    // For now, return mock data until the actual FFI bridge is implemented
    // This simulates the Rust backend response
    await Future.delayed(const Duration(seconds: 2));

    return {
      'authenticity_score': 0.78,
      'analyses': [
        {
          'analysis_type': 'Error Level Analysis',
          'confidence': 0.85,
          'description': 'High compression artifacts detected',
          'regions': [
            {
              'x': 100,
              'y': 150,
              'width': 200,
              'height': 100,
              'confidence': 0.9,
              'manipulation_type': 'Compression Artifacts',
            },
          ],
        },
      ],
      'manipulation_regions': [],
      'processing_time_ms': 2000,
      'analysis_images': [
        {
          'analysis_type': 'ela',
          'image_data': imageData.data, // Mock: return original image data
        },
      ],
    };
  }
}

/// Data structures for FFI communication
class ImageDataBridge {
  final Uint8List data;
  final int width;
  final int height;
  final String format;
  final String colorSpace;

  const ImageDataBridge({
    required this.data,
    required this.width,
    required this.height,
    required this.format,
    required this.colorSpace,
  });

  Map<String, dynamic> toJson() => {
    'data': data,
    'width': width,
    'height': height,
    'format': format,
    'color_space': colorSpace,
  };

  factory ImageDataBridge.fromJson(Map<String, dynamic> json) =>
      ImageDataBridge(
        data: json['data'] as Uint8List,
        width: json['width'] as int,
        height: json['height'] as int,
        format: json['format'] as String,
        colorSpace: json['color_space'] as String,
      );
}

class RectBridge {
  final int x;
  final int y;
  final int width;
  final int height;

  const RectBridge({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  Map<String, dynamic> toJson() => {
    'x': x,
    'y': y,
    'width': width,
    'height': height,
  };

  factory RectBridge.fromJson(Map<String, dynamic> json) => RectBridge(
    x: json['x'] as int,
    y: json['y'] as int,
    width: json['width'] as int,
    height: json['height'] as int,
  );
}

/// Filter types supported by the Rust backend
enum FilterType {
  // Basic filters
  blur,
  sharpen,
  emboss,
  edgeDetect,

  // Adjustment filters
  brightness,
  contrast,
  saturation,
  hue,
  gamma,
  exposure,
  highlights,
  shadows,

  // Artistic filters
  oilPainting,
  watercolor,
  pencilSketch,
  cartoon,
  vintage,
  sepia,
  blackAndWhite,

  // Portrait filters
  skinSmooth,
  eyeBrighten,
  teethWhiten,

  // Landscape filters
  skyEnhance,
  foliageBoost,

  // Architecture filters
  structureEnhance,
  perspectiveCorrect,

  // Food filters
  foodVibrant,
  warmTone,

  // Custom filter
  custom,
}

/// Image formats supported by the system
enum ImageFormat { jpeg, png, webp, bmp, gif, tiff, heic, avif, ico, raw }

/// Color spaces supported by the system
enum ColorSpace { srgb, displayP3, rec2020, adobeRgb }

/// Extension methods for enum conversions
extension FilterTypeExtension on FilterType {
  String get rustName {
    switch (this) {
      case FilterType.blur:
        return 'Blur';
      case FilterType.sharpen:
        return 'Sharpen';
      case FilterType.emboss:
        return 'Emboss';
      case FilterType.edgeDetect:
        return 'EdgeDetect';
      case FilterType.brightness:
        return 'Brightness';
      case FilterType.contrast:
        return 'Contrast';
      case FilterType.saturation:
        return 'Saturation';
      case FilterType.hue:
        return 'Hue';
      case FilterType.gamma:
        return 'Gamma';
      case FilterType.exposure:
        return 'Exposure';
      case FilterType.highlights:
        return 'Highlights';
      case FilterType.shadows:
        return 'Shadows';
      case FilterType.oilPainting:
        return 'OilPainting';
      case FilterType.watercolor:
        return 'Watercolor';
      case FilterType.pencilSketch:
        return 'PencilSketch';
      case FilterType.cartoon:
        return 'Cartoon';
      case FilterType.vintage:
        return 'Vintage';
      case FilterType.sepia:
        return 'Sepia';
      case FilterType.blackAndWhite:
        return 'BlackAndWhite';
      case FilterType.skinSmooth:
        return 'SkinSmooth';
      case FilterType.eyeBrighten:
        return 'EyeBrighten';
      case FilterType.teethWhiten:
        return 'TeethWhiten';
      case FilterType.skyEnhance:
        return 'SkyEnhance';
      case FilterType.foliageBoost:
        return 'FoliageBoost';
      case FilterType.structureEnhance:
        return 'StructureEnhance';
      case FilterType.perspectiveCorrect:
        return 'PerspectiveCorrect';
      case FilterType.foodVibrant:
        return 'FoodVibrant';
      case FilterType.warmTone:
        return 'WarmTone';
      case FilterType.custom:
        return 'Custom';
    }
  }
}

extension ImageFormatExtension on ImageFormat {
  String get rustName {
    switch (this) {
      case ImageFormat.jpeg:
        return 'Jpeg';
      case ImageFormat.png:
        return 'Png';
      case ImageFormat.webp:
        return 'Webp';
      case ImageFormat.bmp:
        return 'Bmp';
      case ImageFormat.gif:
        return 'Gif';
      case ImageFormat.tiff:
        return 'Tiff';
      case ImageFormat.heic:
        return 'Heic';
      case ImageFormat.avif:
        return 'Avif';
      case ImageFormat.ico:
        return 'Ico';
      case ImageFormat.raw:
        return 'Raw';
    }
  }
}

extension ColorSpaceExtension on ColorSpace {
  String get rustName {
    switch (this) {
      case ColorSpace.srgb:
        return 'Srgb';
      case ColorSpace.displayP3:
        return 'DisplayP3';
      case ColorSpace.rec2020:
        return 'Rec2020';
      case ColorSpace.adobeRgb:
        return 'AdobeRgb';
    }
  }
}
