import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Storage Service for Persistent Data
///
/// Provides a unified interface for storing and retrieving data
/// using SharedPreferences. Supports various data types including
/// custom objects through JSON serialization.
class StorageService {
  final SharedPreferences _prefs;

  StorageService(this._prefs);

  // String operations
  Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  String? getString(String key) {
    return _prefs.getString(key);
  }

  // Integer operations
  Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  int? getInt(String key) {
    return _prefs.getInt(key);
  }

  // Double operations
  Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  // Boolean operations
  Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  // String list operations
  Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }

  List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  // JSON object operations
  Future<bool> setObject(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    return await _prefs.setString(key, jsonString);
  }

  Map<String, dynamic>? getObject(String key) {
    final jsonString = _prefs.getString(key);
    if (jsonString == null) return null;

    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  // JSON array operations
  Future<bool> setObjectList(
    String key,
    List<Map<String, dynamic>> value,
  ) async {
    final jsonString = jsonEncode(value);
    return await _prefs.setString(key, jsonString);
  }

  List<Map<String, dynamic>>? getObjectList(String key) {
    final jsonString = _prefs.getString(key);
    if (jsonString == null) return null;

    try {
      final decoded = jsonDecode(jsonString) as List;
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      return null;
    }
  }

  // Remove operations
  Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  Future<bool> clear() async {
    return await _prefs.clear();
  }

  // Check if key exists
  bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  // Get all keys
  Set<String> getKeys() {
    return _prefs.getKeys();
  }

  // Reload preferences from storage
  Future<void> reload() async {
    await _prefs.reload();
  }
}

/// Storage Keys Constants
///
/// Centralized location for all storage keys used throughout the app
class StorageKeys {
  // App settings
  static const String onboardingCompleted = 'onboarding_completed';
  static const String appLanguage = 'app_language';
  static const String themeMode = 'theme_mode';
  static const String autoSaveEnabled = 'auto_save_enabled';
  static const String highQualityProcessing = 'high_quality_processing';
  static const String gpuAcceleration = 'gpu_acceleration';

  // User preferences
  static const String defaultImageQuality = 'default_image_quality';
  static const String defaultCollageLayout = 'default_collage_layout';
  static const String recentFilters = 'recent_filters';
  static const String favoriteFilters = 'favorite_filters';

  // Cache and temporary data
  static const String recentImages = 'recent_images';
  static const String recentCollages = 'recent_collages';
  static const String processingHistory = 'processing_history';

  // Gallery data
  static const String savedImages = 'saved_images';
  static const String savedCollages = 'saved_collages';
  static const String galleryItems = 'gallery_items';

  // Analytics and usage
  static const String appUsageStats = 'app_usage_stats';
  static const String featureUsageCount = 'feature_usage_count';
  static const String lastAppVersion = 'last_app_version';

  // Performance settings
  static const String maxImageSize = 'max_image_size';
  static const String parallelProcessing = 'parallel_processing';
  static const String memoryOptimization = 'memory_optimization';
}

/// Storage Service Extensions
///
/// Provides convenient methods for common storage operations
extension StorageServiceExtensions on StorageService {
  // App settings helpers
  Future<bool> setOnboardingCompleted(bool completed) =>
      setBool(StorageKeys.onboardingCompleted, completed);

  bool getOnboardingCompleted() =>
      getBool(StorageKeys.onboardingCompleted) ?? false;

  Future<bool> setAppLanguage(String languageCode) =>
      setString(StorageKeys.appLanguage, languageCode);

  String getAppLanguage() => getString(StorageKeys.appLanguage) ?? 'en';

  Future<bool> setThemeMode(String mode) =>
      setString(StorageKeys.themeMode, mode);

  String getThemeMode() => getString(StorageKeys.themeMode) ?? 'system';

  // Image quality helpers
  Future<bool> setDefaultImageQuality(int quality) =>
      setInt(StorageKeys.defaultImageQuality, quality);

  int getDefaultImageQuality() => getInt(StorageKeys.defaultImageQuality) ?? 85;

  // Recent items helpers
  Future<bool> addRecentImage(String imageId) async {
    final recentImages = getStringList(StorageKeys.recentImages) ?? [];
    recentImages.remove(imageId); // Remove if already exists
    recentImages.insert(0, imageId); // Add to beginning

    // Keep only last 50 items
    if (recentImages.length > 50) {
      recentImages.removeRange(50, recentImages.length);
    }

    return setStringList(StorageKeys.recentImages, recentImages);
  }

  List<String> getRecentImages() =>
      getStringList(StorageKeys.recentImages) ?? [];

  Future<bool> addRecentCollage(String collageId) async {
    final recentCollages = getStringList(StorageKeys.recentCollages) ?? [];
    recentCollages.remove(collageId); // Remove if already exists
    recentCollages.insert(0, collageId); // Add to beginning

    // Keep only last 20 items
    if (recentCollages.length > 20) {
      recentCollages.removeRange(20, recentCollages.length);
    }

    return setStringList(StorageKeys.recentCollages, recentCollages);
  }

  List<String> getRecentCollages() =>
      getStringList(StorageKeys.recentCollages) ?? [];

  // Filter helpers
  Future<bool> addFavoriteFilter(String filterId) async {
    final favoriteFilters = getStringList(StorageKeys.favoriteFilters) ?? [];
    if (!favoriteFilters.contains(filterId)) {
      favoriteFilters.add(filterId);
      return setStringList(StorageKeys.favoriteFilters, favoriteFilters);
    }
    return true;
  }

  Future<bool> removeFavoriteFilter(String filterId) async {
    final favoriteFilters = getStringList(StorageKeys.favoriteFilters) ?? [];
    favoriteFilters.remove(filterId);
    return setStringList(StorageKeys.favoriteFilters, favoriteFilters);
  }

  List<String> getFavoriteFilters() =>
      getStringList(StorageKeys.favoriteFilters) ?? [];

  bool isFavoriteFilter(String filterId) =>
      getFavoriteFilters().contains(filterId);

  // Usage statistics helpers
  Future<bool> incrementFeatureUsage(String featureName) async {
    final usageStats = getObject(StorageKeys.featureUsageCount) ?? {};
    final currentCount = usageStats[featureName] as int? ?? 0;
    usageStats[featureName] = currentCount + 1;
    return setObject(StorageKeys.featureUsageCount, usageStats);
  }

  int getFeatureUsageCount(String featureName) {
    final usageStats = getObject(StorageKeys.featureUsageCount) ?? {};
    return usageStats[featureName] as int? ?? 0;
  }

  Map<String, dynamic> getAllFeatureUsageStats() =>
      getObject(StorageKeys.featureUsageCount) ?? {};

  // Gallery items helpers
  Future<bool> saveGalleryItem(Map<String, dynamic> item) async {
    final galleryItems = getObjectList(StorageKeys.galleryItems) ?? [];

    // Remove existing item with same id if exists
    galleryItems.removeWhere((existing) => existing['id'] == item['id']);

    // Add new item to beginning
    galleryItems.insert(0, item);

    return setObjectList(StorageKeys.galleryItems, galleryItems);
  }

  List<Map<String, dynamic>> getGalleryItems() =>
      getObjectList(StorageKeys.galleryItems) ?? [];

  Future<bool> deleteGalleryItem(String itemId) async {
    final galleryItems = getObjectList(StorageKeys.galleryItems) ?? [];
    galleryItems.removeWhere((item) => item['id'] == itemId);
    return setObjectList(StorageKeys.galleryItems, galleryItems);
  }

  Future<bool> deleteGalleryItems(List<String> itemIds) async {
    final galleryItems = getObjectList(StorageKeys.galleryItems) ?? [];
    galleryItems.removeWhere((item) => itemIds.contains(item['id']));
    return setObjectList(StorageKeys.galleryItems, galleryItems);
  }

  Map<String, dynamic>? getGalleryItem(String itemId) {
    final galleryItems = getGalleryItems();
    try {
      return galleryItems.firstWhere((item) => item['id'] == itemId);
    } catch (e) {
      return null;
    }
  }

  Future<bool> clearGalleryItems() async {
    return setObjectList(StorageKeys.galleryItems, []);
  }
}
